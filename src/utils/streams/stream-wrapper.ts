import type { ClientReadableStream } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { transform, tryCatch } from '@kdt310722/utils/function'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Awaitable, createDeferred, sleep, tap, withTimeout } from '@kdt310722/utils/promise'
import { IdleTimeoutError } from '../../errors/idle-timeout-error'
import { StreamError } from '../../errors'
import { isIgnorableGrpcError } from '../grpc'

export type ClientReadableStreamWithClose<T> = ClientReadableStream<T> & {
    close: () => Promise<void>
}

export type OnStreamChangeFn<T> = (stream: ClientReadableStreamWithClose<T> | null) => void

export interface StreamWrapperTimeoutOptions {
    subscribe?: number
    close?: number
    idle?: number
}

export interface StreamWrapperResubscribeOptions {
    enabled?: boolean
    retries?: number
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
    autoReset?: boolean
    circuitBreakerTimeout?: number
    shouldResubscribe?: (error: unknown) => boolean
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
    resubscribe?: StreamWrapperResubscribeOptions | boolean
    destroyOnError?: boolean
    destroyOnCloseFail?: boolean
}

export type StreamWrapperEvents<T = unknown> = {
    subscribed: () => void
    closed: (isExplicitly: boolean) => void
    error: (error: unknown) => void
    data: (data: T) => void
    waitForResubscribe: (delay: number) => void
    resubscribe: (attempt: number, retriesLeft: number) => void
    resubscribed: () => void
    resubscribeAbandoned: () => void
    circuitBreakerTripped: (lastResubscribeSuccessTime: number) => void
}

export class StreamWrapper<T = unknown> extends Emitter<StreamWrapperEvents<T>, true> {
    protected readonly subscribeTimeout: number
    protected readonly closeTimeout: number
    protected readonly idleTimeout: number
    protected readonly destroyOnError: boolean
    protected readonly destroyOnCloseFail: boolean
    protected readonly resubscribeOptions: Required<StreamWrapperResubscribeOptions>

    protected stream?: Promise<ClientReadableStreamWithClose<T>>
    protected closePromise?: Promise<void>
    protected resubscribeAttempt = 0
    protected latestError?: unknown
    protected isResubscribing = false
    protected lastResubscribeSuccessTime?: number
    protected idleTimer?: NodeJS.Timeout
    protected lastDataTime?: number

    public constructor(protected readonly subscriber: () => Awaitable<ClientReadableStream<T>>, { timeout = {}, destroyOnError = true, destroyOnCloseFail = true, resubscribe = true }: StreamWrapperOptions = {}) {
        super()

        this.subscribeTimeout = timeout.subscribe ?? 30_000
        this.closeTimeout = timeout.close ?? this.subscribeTimeout
        this.idleTimeout = timeout.idle ?? 60_000
        this.destroyOnError = destroyOnError
        this.destroyOnCloseFail = destroyOnCloseFail
        this.resubscribeOptions = this.resolveResubscribeOptions(resubscribe)
    }

    public async subscribe() {
        await this.closePromise
        await (this.stream ??= this.createStream((stream) => this.stream = notNullish(stream) ? Promise.resolve(stream) : undefined).then(tap(() => this.emit('subscribed'))))
    }

    public async close() {
        await this.stream?.then(async (stream) => this.closePromise ??= stream.close().finally(() => {
            this.stream = undefined
            this.closePromise = undefined
        }))
    }

    protected async createStream(onStreamChange?: OnStreamChangeFn<T>): Promise<ClientReadableStreamWithClose<T>> {
        const promise = createDeferred<void>()
        const stream = await this.subscriber()

        let metadataHandler: () => void
        let errorHandler: (error: Error) => void
        let closeHandler: () => void
        let endHandler: () => void
        let dataHandler: (data: T) => void

        const cleanup = () => {
            stream.removeListener('metadata', metadataHandler)
            stream.removeListener('error', errorHandler)
            stream.removeListener('close', closeHandler)
            stream.removeListener('end', endHandler)
            stream.removeListener('data', dataHandler)
        }

        let isExplicitlyClosed = false

        stream.on('metadata', metadataHandler = () => promise.isSettled || promise.resolve())
        stream.on('error', errorHandler = (error) => (promise.isSettled ? this.handleError(stream, error) : promise.reject(error)))
        stream.on('close', closeHandler = () => (promise.isSettled ? this.handleClose(stream, cleanup, isExplicitlyClosed, onStreamChange) : promise.reject(new StreamError('Stream closed unexpectedly').withStream(stream))))
        stream.on('end', endHandler = () => (promise.isSettled ? stream.destroy() : promise.reject(new StreamError('Stream ended unexpectedly').withStream(stream))))
        stream.on('data', dataHandler = (data: T) => (promise.isSettled ? this.handleData(stream, data) : promise.resolve()))

        await withTimeout(promise, this.subscribeTimeout, () => new StreamError('Subscribe timeout').withStream(stream)).catch((error) => {
            cleanup()
            tryCatch(() => stream.destroy(), null)

            throw error
        })

        this.runIdleTimer(stream)

        const close = async () => {
            return Promise.resolve(isExplicitlyClosed = true).then(() => this.closeStream(stream))
        }

        return Object.assign(stream, { close })
    }

    protected async closeStream(stream: ClientReadableStream<T>) {
        const promise = createDeferred<void>()

        let closeHandler: () => void
        let errorHandler: (error: Error) => void

        stream.once('close', closeHandler = () => promise.resolve())
        stream.once('error', errorHandler = (error) => promise.reject(error))
        stream.cancel()

        const handleError = (error: Error) => {
            if (this.destroyOnCloseFail) {
                stream.destroy(error)
            } else {
                throw error
            }
        }

        await withTimeout(promise, this.closeTimeout, () => new StreamError('Close timeout').withStream(stream)).catch(handleError).finally(() => {
            stream.removeListener('close', closeHandler)
            stream.removeListener('error', errorHandler)
        })
    }

    protected handleData(stream: ClientReadableStream<T>, data: T) {
        this.emit('data', data)
        this.lastDataTime = Date.now()
        this.runIdleTimer(stream)
    }

    protected runIdleTimer(stream: ClientReadableStream<T>) {
        if (this.idleTimer) {
            clearTimeout(this.idleTimer)
        }

        this.idleTimer = setTimeout(() => this.handleError(stream, new StreamError('Idle timeout').withStream(stream).withValue('lastDataTime', this.lastDataTime)), this.idleTimeout)
    }

    protected handleError(stream: ClientReadableStream<T>, error: unknown) {
        if (isIgnorableGrpcError(error)) {
            return
        }

        this.emit('error', this.latestError = error)

        if (this.destroyOnError) {
            stream.destroy()
        }
    }

    protected handleClose(stream: ClientReadableStream<T>, cleanup: () => void, isExplicitly: boolean, onStreamChange?: OnStreamChangeFn<T>) {
        cleanup()

        this.emit('closed', isExplicitly)

        if (isExplicitly) {
            this.resubscribeAttempt = 0
            this.lastResubscribeSuccessTime = undefined
        } else {
            this.handleResubscribe(stream, onStreamChange)
        }

        clearTimeout(this.idleTimer)

        this.latestError = undefined
        this.idleTimer = undefined
        this.lastDataTime = undefined
    }

    protected handleResubscribe(stream: ClientReadableStream<T>, onStreamChange?: OnStreamChangeFn<T>) {
        if (this.isResubscribing || !this.resubscribeOptions.enabled || !this.resubscribeOptions.shouldResubscribe(this.latestError)) {
            return this.emit('resubscribeAbandoned')
        }

        if (notNullish(this.lastResubscribeSuccessTime) && Date.now() - this.lastResubscribeSuccessTime < this.resubscribeOptions.circuitBreakerTimeout) {
            this.emit('circuitBreakerTripped', this.lastResubscribeSuccessTime)
            this.emit('resubscribeAbandoned')

            return
        }

        this.isResubscribing = true

        this.resubscribe(stream, onStreamChange).then((newStream) => this.handleResubscribeResult(newStream, onStreamChange)).finally(() => {
            this.isResubscribing = false
        })

        return true
    }

    protected async resubscribe(stream: ClientReadableStream<T>, onStreamChange?: OnStreamChangeFn<T>): Promise<ClientReadableStreamWithClose<T> | null> {
        if (this.resubscribeAttempt >= this.resubscribeOptions.retries) {
            return null
        }

        const delay = this.getResubscribeDelay(++this.resubscribeAttempt)

        if (delay > 0) {
            this.emit('waitForResubscribe', delay)
        }

        await sleep(delay).then(() => this.emit('resubscribe', this.resubscribeAttempt, this.resubscribeOptions.retries - this.resubscribeAttempt))

        return this.createStream((newStream) => onStreamChange?.(newStream)).catch((error) => (
            transform(this.emit('error', error), () => (this.resubscribeOptions.shouldResubscribe(error) ? this.resubscribe(stream, onStreamChange) : null))
        ))
    }

    protected handleResubscribeResult(newStream: ClientReadableStreamWithClose<T> | null, onStreamChange?: OnStreamChangeFn<T>) {
        onStreamChange?.(newStream)

        if (notNullish(newStream)) {
            this.lastResubscribeSuccessTime = Date.now()

            if (this.resubscribeOptions.autoReset) {
                this.resubscribeAttempt = 0
            }

            this.emit('resubscribed')
        } else {
            this.emit('resubscribeAbandoned')
        }
    }

    protected getResubscribeDelay(attempts: number) {
        const baseDelay = Math.min(this.resubscribeOptions.delay * this.resubscribeOptions.backoff ** (attempts - 1), this.resubscribeOptions.maxDelay)

        if (this.resubscribeOptions.jitter > 0) {
            return transform(baseDelay * this.resubscribeOptions.jitter, (jitter) => baseDelay - (jitter / 2) + (Math.random() * jitter))
        }

        return baseDelay
    }

    protected resolveResubscribeOptions(options: StreamWrapperResubscribeOptions | boolean): Required<StreamWrapperResubscribeOptions> {
        return transform(resolveNestedOptions(options) || { enabled: false }, ({ enabled = true, retries = 5, delay = 1000, backoff = 2, jitter = 0, maxDelay = 10_000, autoReset = true, shouldResubscribe = () => true, circuitBreakerTimeout = 10_000 }) => ({ enabled, retries, delay, backoff, jitter, maxDelay, autoReset, shouldResubscribe, circuitBreakerTimeout }))
    }
}
